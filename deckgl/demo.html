<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>MapSets Demo</title>

        <!-- 引入 React 和 ReactDOM -->
        <script
            crossorigin
            src="https://unpkg.com/react@18/umd/react.development.js"
        ></script>
        <script
            crossorigin
            src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"
        ></script>

        <!-- 引入 Redux -->
        <script src="https://unpkg.com/redux@4/dist/redux.min.js"></script>
        <script src="https://unpkg.com/react-redux@8/dist/react-redux.min.js"></script>

        <!-- 引入 Ant Design -->
        <link rel="stylesheet" href="https://unpkg.com/antd@5/dist/reset.css" />
        <script src="https://unpkg.com/antd@5/dist/antd.min.js"></script>

        <!-- 引入 Deck.GL 相关依赖 -->
        <script src="https://unpkg.com/deck.gl@9/dist.min.js"></script>
        <script src="https://unpkg.com/maplibre-gl@4/dist/maplibre-gl.js"></script>
        <link
            href="https://unpkg.com/maplibre-gl@4/dist/maplibre-gl.css"
            rel="stylesheet"
        />

        <!-- 引入组件样式 -->
        <link rel="stylesheet" href="./dist/style.css" />

        <style>
            body {
                margin: 0;
                padding: 20px;
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI",
                    "Roboto", sans-serif;
                background-color: #f5f5f5;
            }

            .demo-container {
                max-width: 1200px;
                margin: 0 auto;
                background: white;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                overflow: hidden;
            }

            .demo-header {
                padding: 20px;
                background: #001529;
                color: white;
                text-align: center;
            }

            .demo-content {
                padding: 20px;
            }

            .map-container {
                width: 100%;
                height: 600px;
                border: 1px solid #d9d9d9;
                border-radius: 6px;
                overflow: hidden;
            }

            .controls {
                margin-bottom: 20px;
                padding: 16px;
                background: #fafafa;
                border-radius: 6px;
            }

            .control-group {
                margin-bottom: 12px;
            }

            .control-group label {
                display: inline-block;
                width: 120px;
                font-weight: 500;
            }

            .control-group button {
                margin-right: 8px;
                padding: 4px 12px;
                border: 1px solid #d9d9d9;
                background: white;
                border-radius: 4px;
                cursor: pointer;
            }

            .control-group button:hover {
                border-color: #1890ff;
                color: #1890ff;
            }

            .control-group button.active {
                background: #1890ff;
                color: white;
                border-color: #1890ff;
            }
        </style>
    </head>
    <body>
        <div class="demo-container">
            <div class="demo-header">
                <h1>MapSets 组件演示</h1>
                <p>基于 Deck.GL 的地图可视化组件库</p>
            </div>

            <div class="demo-content">
                <div class="controls">
                    <div class="control-group">
                        <label>地图主题:</label>
                        <button id="theme-light" class="active">
                            浅色主题
                        </button>
                        <button id="theme-dark">深色主题</button>
                    </div>
                    <div class="control-group">
                        <label>视图模式:</label>
                        <button id="view-2d" class="active">2D 视图</button>
                        <button id="view-3d">3D 视图</button>
                    </div>
                    <div class="control-group">
                        <label>工具:</label>
                        <button id="tool-geojson">GeoJSON 工具</button>
                        <button id="tool-reset">重置视图</button>
                    </div>
                </div>

                <div id="map-root" class="map-container"></div>
            </div>
        </div>

        <!-- 引入 dayjs -->
        <script src="https://unpkg.com/dayjs@1/dayjs.min.js"></script>
        <script src="https://unpkg.com/dayjs@1/plugin/utc.js"></script>
        <script src="https://unpkg.com/dayjs@1/plugin/timezone.js"></script>

        <!-- 引入其他依赖 -->
        <script src="https://unpkg.com/lodash@4/lodash.min.js"></script>
        <script src="https://unpkg.com/@turf/turf@6/turf.min.js"></script>

        <script>
            // 创建一个简单的演示，不使用实际的组件
            document.addEventListener("DOMContentLoaded", function () {
                // 初始化 dayjs 插件
                if (typeof dayjs !== "undefined") {
                    dayjs.extend(dayjs_plugin_utc);
                    dayjs.extend(dayjs_plugin_timezone);
                }

                // 创建一个简单的地图演示
                function createMapDemo() {
                    const mapContainer = document.getElementById("map-root");

                    // 创建基础的 MapLibre GL 地图
                    if (typeof maplibregl !== "undefined") {
                        const map = new maplibregl.Map({
                            container: mapContainer,
                            style: "https://demotiles.maplibre.org/style.json",
                            center: [116.3974, 39.9093], // 北京坐标
                            zoom: 10,
                        });

                        map.on("load", function () {
                            console.log("地图加载完成");

                            // 添加一个简单的标记
                            new maplibregl.Marker()
                                .setLngLat([116.3974, 39.9093])
                                .addTo(map);
                        });

                        // 绑定控制按钮事件
                        bindControlEvents(map);
                    } else {
                        mapContainer.innerHTML = `
                                <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #1890ff;">
                                    <div style="text-align: center;">
                                        <h3>地图演示</h3>
                                        <p>MapLibre GL 正在加载中...</p>
                                        <p>这里将显示基于 Deck.GL 的地图组件</p>
                                    </div>
                                </div>
                            `;
                    }
                }

                // 绑定控制按钮事件
                function bindControlEvents(map) {
                    // 主题切换
                    document
                        .getElementById("theme-light")
                        ?.addEventListener("click", function () {
                            console.log("切换到浅色主题");
                            document
                                .querySelectorAll('[id^="theme-"]')
                                .forEach((btn) =>
                                    btn.classList.remove("active")
                                );
                            this.classList.add("active");
                        });

                    document
                        .getElementById("theme-dark")
                        ?.addEventListener("click", function () {
                            console.log("切换到深色主题");
                            document
                                .querySelectorAll('[id^="theme-"]')
                                .forEach((btn) =>
                                    btn.classList.remove("active")
                                );
                            this.classList.add("active");
                        });

                    // 视图模式切换
                    document
                        .getElementById("view-2d")
                        ?.addEventListener("click", function () {
                            console.log("切换到2D视图");
                            document
                                .querySelectorAll('[id^="view-"]')
                                .forEach((btn) =>
                                    btn.classList.remove("active")
                                );
                            this.classList.add("active");
                        });

                    document
                        .getElementById("view-3d")
                        ?.addEventListener("click", function () {
                            console.log("切换到3D视图");
                            document
                                .querySelectorAll('[id^="view-"]')
                                .forEach((btn) =>
                                    btn.classList.remove("active")
                                );
                            this.classList.add("active");
                        });

                    // GeoJSON 工具
                    document
                        .getElementById("tool-geojson")
                        ?.addEventListener("click", function () {
                            console.log("GeoJSON 工具被点击");
                            alert("GeoJSON 工具功能演示");
                        });

                    // 重置视图
                    document
                        .getElementById("tool-reset")
                        ?.addEventListener("click", function () {
                            console.log("重置视图");
                            if (map) {
                                map.flyTo({
                                    center: [116.3974, 39.9093],
                                    zoom: 10,
                                });
                            }
                        });
                }

                // 启动演示
                createMapDemo();

                console.log("MapSets 演示页面已加载");
                console.log("可用的全局对象:", {
                    React: typeof React,
                    ReactDOM: typeof ReactDOM,
                    maplibregl: typeof maplibregl,
                    deck: typeof deck,
                    dayjs: typeof dayjs,
                });
            });
        </script>
    </body>
</html>
